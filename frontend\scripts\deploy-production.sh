#!/bin/bash

# 🔥 生产环境部署脚本
# 确保服务器端彻底清理缓存并重新构建

set -e  # 遇到错误立即退出

echo "🚀 开始生产环境部署..."

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误：请在frontend目录下运行此脚本"
    exit 1
fi

# 1. 彻底清理所有缓存
echo "🧹 步骤1: 彻底清理构建缓存..."
rm -rf dist
rm -rf node_modules/.vite
rm -rf node_modules/.cache
rm -rf .vite
rm -rf .cache
rm -f tsconfig.tsbuildinfo
rm -f .eslintcache
rm -f vite.config.*.timestamp-*

# 2. 清理npm缓存
echo "🧽 步骤2: 清理npm缓存..."
npm cache clean --force || echo "⚠️  npm缓存清理失败，继续..."

# 3. 重新安装依赖
echo "📦 步骤3: 重新安装依赖..."
npm ci

# 4. 强制生产环境构建（包含后处理清理）
echo "🔨 步骤4: 执行生产环境构建..."
NODE_ENV=production npm run build

# 5. 验证构建结果
echo "🔍 步骤5: 验证构建结果..."
npm run verify

# 6. 检查关键文件
echo "📋 步骤6: 检查构建产物..."
if [ ! -d "dist" ]; then
    echo "❌ 错误：dist目录不存在"
    exit 1
fi

if [ ! -f "dist/index.html" ]; then
    echo "❌ 错误：index.html不存在"
    exit 1
fi

# 7. 最终验证：搜索可能的HMR代码
echo "🔎 步骤7: 最终HMR代码检查..."
HMR_COUNT=$(grep -r "5173\|hmr\|vite.*client" dist/ 2>/dev/null | wc -l || echo "0")

if [ "$HMR_COUNT" -gt "0" ]; then
    echo "⚠️  警告：发现 $HMR_COUNT 处可能的HMR代码"
    echo "🔍 详细信息："
    grep -r "5173\|hmr\|vite.*client" dist/ 2>/dev/null || true
    echo ""
    echo "❓ 是否继续部署？这些可能是第三方库的无害代码"
    read -p "输入 'yes' 继续，其他任意键取消: " confirm
    if [ "$confirm" != "yes" ]; then
        echo "❌ 部署已取消"
        exit 1
    fi
else
    echo "✅ 未发现HMR代码，构建纯净"
fi

echo ""
echo "🎉 生产环境构建完成！"
echo "📊 构建统计："
echo "   📁 dist目录大小: $(du -sh dist | cut -f1)"
echo "   📄 文件数量: $(find dist -type f | wc -l)"
echo ""
echo "🚀 可以安全部署到生产环境"
echo ""
echo "📋 部署命令："
echo "   git add ."
echo "   git commit -m \"修复版：彻底解决Vite HMR生产环境错误 v2.0\""
echo "   git push origin master"
echo ""
echo "🖥️  服务器部署："
echo "   ssh root@*************"
echo "   cd /var/www/mianshijun"
echo "   git pull origin master"
echo "   cd frontend"
echo "   ./scripts/deploy-production.sh"
echo "   pm2 restart mianshijun-frontend"
