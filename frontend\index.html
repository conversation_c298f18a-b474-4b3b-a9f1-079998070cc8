<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI Interview Assistant Homepage</title>
    <!-- 🔥 强制禁用缓存和开发工具 -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <!-- 🔥 生产环境安全策略 -->
    <meta http-equiv="Content-Security-Policy" content="connect-src 'self' https: wss://mianshijun.xyz; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';" />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <!-- 🔥 生产环境HMR阻断脚本 -->
    <script>
      // 强制禁用所有HMR相关功能
      if (typeof window !== 'undefined') {
        window.__vite__hmr = undefined;
        window.__vite__client = undefined;
        window.import = undefined;
        if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
          // 生产环境：阻断所有到localhost:5173的连接
          const originalWebSocket = window.WebSocket;
          window.WebSocket = function(url, protocols) {
            if (url && url.includes('5173')) {
              console.warn('🚫 生产环境阻断HMR连接:', url);
              return { close: () => {}, send: () => {} };
            }
            return new originalWebSocket(url, protocols);
          };
        }
      }
    </script>
  </body>
</html>
