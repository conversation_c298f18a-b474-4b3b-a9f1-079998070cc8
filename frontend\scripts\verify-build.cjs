#!/usr/bin/env node

/**
 * 构建验证脚本
 * 检查生产构建是否包含HMR相关代码
 */

const fs = require('fs');
const path = require('path');

const DIST_DIR = path.join(__dirname, '../dist');
// 🔥 增强版：关键的HMR模式 - 这些必须在生产环境中移除
const CRITICAL_HMR_PATTERNS = [
  // Vite HMR相关
  /ws:\/\/.*:5173/g,
  /wss:\/\/.*:5173/g,
  /WebSocket.*5173/g,
  /__vite__hmr/g,
  /__vite__client/g,
  /import\.meta\.hot/g,
  /\[vite\]/g,
  /server connection lost/gi,
  /polling for restart/gi,
  /connecting\.\.\./gi,
  // 🔥 新增：React Fast Refresh相关
  /__react_refresh__/g,
  /\$RefreshReg\$/g,
  /\$RefreshSig\$/g,
  /__REACT_DEVTOOLS_GLOBAL_HOOK__/g,
  /react-refresh/gi,
  /fast.?refresh/gi,
  // 🔥 新增：更多Vite客户端代码
  /@vite\/client/g,
  /vite\/client/g,
  /hmr\.accept/g,
  /hmr\.dispose/g,
  /module\.hot/g,
  /webpackHotUpdate/g,
  // 🔥 新增：端口5173的其他形式
  /:5173/g,
  /localhost:5173/g,
  /127\.0\.0\.1:5173/g,
];

// 第三方库的热更新模式 - 这些通常是无害的
const THIRD_PARTY_HMR_PATTERNS = [
  /hotBeforeUpdate/g,
  /componentDidUpdate/g,
  /hot.*reload/gi,
];

function checkFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const criticalIssues = [];
  const thirdPartyIssues = [];

  // 检查关键HMR模式
  CRITICAL_HMR_PATTERNS.forEach((pattern, index) => {
    const matches = content.match(pattern);
    if (matches) {
      criticalIssues.push({
        pattern: pattern.toString(),
        matches: matches.length,
        examples: matches.slice(0, 3),
        type: 'critical'
      });
    }
  });

  // 检查第三方库HMR模式
  THIRD_PARTY_HMR_PATTERNS.forEach((pattern, index) => {
    const matches = content.match(pattern);
    if (matches) {
      thirdPartyIssues.push({
        pattern: pattern.toString(),
        matches: matches.length,
        examples: matches.slice(0, 3),
        type: 'third-party'
      });
    }
  });

  return { criticalIssues, thirdPartyIssues };
}

function scanDirectory(dir) {
  const results = [];
  
  function scan(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    items.forEach(item => {
      const itemPath = path.join(currentDir, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        scan(itemPath);
      } else if (stat.isFile() && (item.endsWith('.js') || item.endsWith('.css') || item.endsWith('.html'))) {
        const { criticalIssues, thirdPartyIssues } = checkFile(itemPath);
        if (criticalIssues.length > 0 || thirdPartyIssues.length > 0) {
          results.push({
            file: path.relative(DIST_DIR, itemPath),
            criticalIssues,
            thirdPartyIssues
          });
        }
      }
    });
  }
  
  scan(dir);
  return results;
}

function main() {
  console.log('🔍 开始验证生产构建...');
  console.log(`📁 扫描目录: ${DIST_DIR}`);
  
  if (!fs.existsSync(DIST_DIR)) {
    console.error('❌ 构建目录不存在，请先运行构建命令');
    process.exit(1);
  }
  
  const results = scanDirectory(DIST_DIR);

  // 统计关键问题和第三方问题
  const criticalResults = results.filter(r => r.criticalIssues.length > 0);
  const thirdPartyResults = results.filter(r => r.thirdPartyIssues.length > 0);

  if (criticalResults.length === 0) {
    console.log('✅ 验证通过！生产构建中未发现关键HMR代码');

    if (thirdPartyResults.length > 0) {
      console.log('ℹ️  发现第三方库的热更新代码（这是正常的）:');
      thirdPartyResults.forEach(result => {
        console.log(`   📄 ${result.file}: ${result.thirdPartyIssues.length} 个第三方热更新模式`);
      });
      console.log('');
    }

    console.log('🎉 可以安全部署到生产环境');
  } else {
    console.error('❌ 验证失败！发现关键HMR代码:');
    console.error('');

    criticalResults.forEach(result => {
      console.error(`📄 文件: ${result.file}`);
      result.criticalIssues.forEach(issue => {
        console.error(`   🔍 模式: ${issue.pattern}`);
        console.error(`   📊 匹配数: ${issue.matches}`);
        console.error(`   📝 示例: ${issue.examples.join(', ')}`);
        console.error('');
      });
    });

    if (thirdPartyResults.length > 0) {
      console.log('ℹ️  同时发现第三方库热更新代码（这些是正常的）:');
      thirdPartyResults.forEach(result => {
        console.log(`   📄 ${result.file}: ${result.thirdPartyIssues.length} 个第三方模式`);
      });
      console.log('');
    }

    console.error('⚠️  请检查Vite配置并重新构建');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
