# Vite HMR 生产环境修复方案

## 🔍 问题描述

生产环境网站 https://mianshijun.xyz 在浏览器控制台出现大量错误：
- `GET https://mianshijun.xyz:5173/ net::ERR_SSL_PROTOCOL_ERROR`
- `WebSocket connection to 'wss://mianshijun.xyz:5173/?token=xxx' failed`
- `[vite] server connection lost. Polling for restart...`

## 🎯 根本原因

Vite的HMR（热模块替换）客户端代码被错误地包含在生产环境构建中，导致浏览器尝试连接不存在的开发服务器端口5173。

## 🛠️ 解决方案

### 1. 更新Vite配置 (`vite.config.ts`)

- ✅ 正确的环境检测和模式配置
- ✅ 生产环境禁用HMR相关全局变量
- ✅ 自定义插件移除HMR代码
- ✅ 生产环境优化配置

### 2. 环境变量配置 (`.env.production`)

- ✅ 明确的生产环境标识
- ✅ 禁用开发工具和HMR
- ✅ 正确的API配置

### 3. 自定义Vite插件 (`vite-plugins/remove-hmr-plugin.ts`)

- ✅ 构建时检测和移除HMR代码
- ✅ 强制禁用HMR相关全局变量
- ✅ 生成bundle时的代码检查

### 4. 构建验证脚本 (`scripts/verify-build.js`)

- ✅ 自动检测构建产物中的HMR代码
- ✅ 详细的错误报告和修复建议
- ✅ 构建流程集成

## 🚀 使用方法

### 本地测试构建

```bash
# 清理并重新构建
cd frontend
rm -rf dist node_modules/.vite
npm run build

# 手动验证（可选）
npm run verify
```

### 服务器部署

```bash
# 1. 提交代码到GitHub
git add .
git commit -m "修复版：彻底解决Vite HMR生产环境错误

- 更新Vite配置正确处理生产环境HMR禁用
- 添加自定义插件移除HMR客户端代码
- 创建构建验证脚本确保代码质量
- 配置环境变量和构建优化
- 解决WebSocket连接5173端口错误"

git push origin master

# 2. 服务器部署
ssh root@*************
cd /var/www/mianshijun
git pull origin master

# 3. 重新构建前端
cd frontend
npm run build

# 4. 重启服务（如果需要）
pm2 restart mianshijun-frontend
```

## 🔧 技术细节

### 关键配置更改

1. **环境变量定义**：
   ```typescript
   define: {
     'import.meta.hot': isProduction ? 'undefined' : 'import.meta.hot',
     'import.meta.env.DEV': JSON.stringify(!isProduction),
     'import.meta.env.PROD': JSON.stringify(isProduction),
   }
   ```

2. **HMR配置**：
   ```typescript
   server: {
     hmr: isProduction ? false : { port: 5173, host: 'localhost' }
   }
   ```

3. **构建优化**：
   ```typescript
   esbuild: isProduction ? {
     drop: ['console', 'debugger']
   } : undefined
   ```

### 验证检查项

- ❌ WebSocket连接到5173端口
- ❌ HMR相关全局变量
- ❌ Vite客户端代码
- ❌ 开发环境特有的错误信息

## 📊 预期效果

- ✅ 生产环境不再出现5173端口连接错误
- ✅ 浏览器控制台清洁无HMR相关错误
- ✅ 构建产物更小，性能更好
- ✅ 自动化验证确保问题不再复现

## 🔄 后续维护

1. **每次构建后自动验证**：`npm run build` 会自动运行验证脚本
2. **手动验证**：`npm run verify` 可随时检查构建产物
3. **监控生产环境**：定期检查浏览器控制台确保无新错误

## 📞 技术支持

如果问题仍然存在，请检查：
1. 浏览器缓存是否已清理
2. CDN缓存是否已刷新
3. 服务器构建是否使用了正确的配置
4. 环境变量是否正确设置
