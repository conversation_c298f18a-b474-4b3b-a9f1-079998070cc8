import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import { removeHmrPlugin } from './vite-plugins/remove-hmr-plugin';

export default defineConfig(({ mode }) => {
  const isProduction = mode === 'production';

  console.log(`🔧 Vite配置: mode=${mode}, isProduction=${isProduction}`);

  return {
    plugins: [
      // 🔥 关键修改：生产环境完全禁用React Fast Refresh
      react({
        // 生产环境禁用babel插件的开发特性，这是HMR代码的主要来源
        babel: isProduction ? {
          plugins: [],
          // 禁用React相关的开发时转换
          presets: []
        } : undefined,
        // 生产环境禁用开发工具集成
        include: isProduction ? "**/*.{jsx,tsx}" : "**/*.{jsx,tsx,js,ts}",
        // 🔥 关键：生产环境禁用jsxImportSource的开发特性
        jsxImportSource: isProduction ? undefined : '@emotion/react'
      }),
      // 生产环境移除HMR代码的自定义插件
      ...(isProduction ? [removeHmrPlugin()] : [])
    ],
    optimizeDeps: {
      exclude: ['lucide-react'],
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '@new-mianshijun/common': path.resolve(__dirname, '../packages/common/src'),
      },
    },
    server: {
      host: true,
      port: 5173,
      strictPort: true,
      allowedHosts: [
        'localhost',
        '127.0.0.1',
        'mianshijun.xyz',
        'www.mianshijun.xyz',
        '*************'
      ],
      // 🔥 关键修改：生产环境完全禁用HMR
      hmr: isProduction ? false : {
        port: 5173,
        host: 'localhost'
      },
      proxy: {
        '/api': {
          target: 'http://localhost:3000',
          changeOrigin: true,
          secure: false,
        },
      },
    },
    build: {
      // 🔥 关键修改：强化生产环境构建配置
      minify: isProduction ? 'esbuild' : false,
      sourcemap: false, // 生产环境不生成sourcemap
      // 强制清理构建缓存
      emptyOutDir: true,
      rollupOptions: {
        output: {
          manualChunks: undefined,
          // 🔥 新增：确保文件名不包含开发环境标识
          entryFileNames: isProduction ? 'assets/[name]-[hash].js' : '[name].js',
          chunkFileNames: isProduction ? 'assets/[name]-[hash].js' : '[name].js',
          assetFileNames: isProduction ? 'assets/[name]-[hash].[ext]' : '[name].[ext]'
        },
        // 🔥 生产环境排除所有可能的开发依赖
        external: isProduction ? [
          // 确保这些开发工具不被打包
          '@vitejs/plugin-react',
          'vite/client',
          '@vite/client'
        ] : undefined,
        // 🔥 新增：生产环境插件过滤
        plugins: isProduction ? [
          {
            name: 'remove-dev-imports',
            generateBundle(_options, bundle) {
              // 移除所有可能的开发环境导入
              Object.keys(bundle).forEach(fileName => {
                const chunk = bundle[fileName];
                if (chunk.type === 'chunk' && chunk.code) {
                  // 移除vite客户端导入
                  chunk.code = chunk.code.replace(/import\s+.*from\s+['"]@?vite\/client['"];?\s*/g, '');
                  chunk.code = chunk.code.replace(/import\s+.*from\s+['"]vite\/client['"];?\s*/g, '');
                  // 移除HMR相关导入
                  chunk.code = chunk.code.replace(/import\.meta\.hot/g, 'undefined');
                }
              });
            }
          }
        ] : []
      },
      // 确保生产环境不包含开发代码
      target: isProduction ? 'es2015' : 'esnext',
    },
    // 🔥 关键修改：更强的环境变量定义
    define: {
      __DEV__: JSON.stringify(!isProduction),
      'process.env.NODE_ENV': JSON.stringify(isProduction ? 'production' : 'development'),
      // 生产环境禁用HMR相关全局变量
      'import.meta.env.DEV': JSON.stringify(!isProduction),
      'import.meta.env.PROD': JSON.stringify(isProduction),
      'import.meta.env.MODE': JSON.stringify(mode),
      // 🔥 更彻底地禁用热更新相关代码
      ...(isProduction && {
        'import.meta.hot': 'undefined',
        '__vite__hmr': 'undefined',
        '__vite__client': 'undefined',
        'module.hot': 'undefined',
        'webpackHotUpdate': 'undefined',
        // 新增：禁用React相关的热更新
        '__REACT_DEVTOOLS_GLOBAL_HOOK__': 'undefined',
        '__react_refresh__': 'undefined',
        '$RefreshReg$': 'undefined',
        '$RefreshSig$': 'undefined',
      }),
    },
    // 🔥 关键修改：生产环境禁用开发工具
    esbuild: isProduction ? {
      drop: ['console', 'debugger'], // 生产环境移除console和debugger
      legalComments: 'none', // 移除法律注释
      minifyIdentifiers: true,
      minifySyntax: true,
      minifyWhitespace: true,
    } : undefined,

    // 🔥 新增：生产环境禁用CSS开发工具
    css: isProduction ? {
      devSourcemap: false,
    } : undefined,
  };
});
