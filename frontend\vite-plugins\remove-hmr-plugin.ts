import type { Plugin } from 'vite';

/**
 * 自定义Vite插件：生产环境移除HMR客户端代码
 * 
 * 这个插件确保在生产构建中完全移除所有HMR相关的代码，
 * 包括WebSocket连接、客户端代码和相关的全局变量。
 */
export function removeHmrPlugin(): Plugin {
  return {
    name: 'remove-hmr-plugin',
    apply: 'build', // 只在构建时应用
    config(config, { mode }) {
      const isProduction = mode === 'production';
      
      if (isProduction) {
        console.log('🚫 RemoveHmrPlugin: 生产环境已激活，将移除所有HMR相关代码');
        
        // 确保define配置存在
        config.define = config.define || {};
        
        // 强制禁用HMR相关的全局变量
        Object.assign(config.define, {
          'import.meta.hot': 'undefined',
          '__vite__hmr': 'undefined',
          '__vite__client': 'undefined',
          'import.meta.env.DEV': 'false',
          'import.meta.env.PROD': 'true',
        });
        
        // 确保server配置不会影响构建
        if (config.server) {
          config.server.hmr = false;
        }
      }
    },
    
    generateBundle(options, bundle) {
      // 在生成bundle时检查并移除HMR相关代码
      Object.keys(bundle).forEach(fileName => {
        const chunk = bundle[fileName];
        
        if (chunk.type === 'chunk' && chunk.code) {
          // 检查是否包含HMR相关代码
          const hmrPatterns = [
            /ws:\/\/.*:5173/g,
            /wss:\/\/.*:5173/g,
            /WebSocket.*5173/g,
            /__vite__hmr/g,
            /import\.meta\.hot/g,
            /\[vite\]/g,
            /server connection lost/gi,
            /Polling for restart/gi,
          ];

          // 第三方库的热更新代码模式（这些通常是无害的）
          const thirdPartyHmrPatterns = [
            /hotBeforeUpdate/g,
            /componentDidUpdate/g,
            /hot.*reload/gi,
          ];
          
          let hasCriticalHmrCode = false;
          let hasThirdPartyHmrCode = false;
          let originalCode = chunk.code;

          // 检查关键的HMR代码（这些必须移除）
          hmrPatterns.forEach(pattern => {
            if (pattern.test(chunk.code)) {
              hasCriticalHmrCode = true;
              console.warn(`⚠️ RemoveHmrPlugin: 在 ${fileName} 中发现关键HMR代码: ${pattern}`);
            }
          });

          // 检查第三方库的热更新代码（这些通常是无害的）
          thirdPartyHmrPatterns.forEach(pattern => {
            if (pattern.test(chunk.code)) {
              hasThirdPartyHmrCode = true;
              console.log(`ℹ️ RemoveHmrPlugin: 在 ${fileName} 中发现第三方热更新代码: ${pattern} (通常无害)`);
            }
          });

          if (hasCriticalHmrCode) {
            console.error(`❌ RemoveHmrPlugin: 文件 ${fileName} 包含关键HMR代码，这不应该发生在生产构建中！`);

            // 尝试移除关键HMR相关代码
            hmrPatterns.forEach(pattern => {
              chunk.code = chunk.code.replace(pattern, '');
            });

            if (chunk.code !== originalCode) {
              console.log(`🔧 RemoveHmrPlugin: 已从 ${fileName} 中移除关键HMR代码`);
            }
          }

          // 对于第三方库的热更新代码，我们只记录但不阻止构建
          if (hasThirdPartyHmrCode && !hasCriticalHmrCode) {
            console.log(`✅ RemoveHmrPlugin: ${fileName} 只包含第三方库的热更新代码，这是正常的`);
          }
        }
      });
    },
    
    writeBundle() {
      console.log('✅ RemoveHmrPlugin: 生产构建完成，HMR代码检查已完成');
    }
  };
}
